# scriptJson 数据结构和字典

## 概述

本文档定义了服务端向Android客户端发送的scriptJson数据结构规范。该JSON数据用于描述单个自动化步骤的执行指令，包含动作类型、参数、成功条件、重试配置等信息。

## 快速参考

### 支持的动作类型（21种）

| 类别 | 动作类型 | 说明 | 主要参数 |
|------|----------|------|----------|
| **基础操作** | `LAUNCH_APP` | 启动应用 | packageName, activityName |
| | `LAUNCH_APP_BY_INTENT` | 通过Intent启动 | action, data, extras |
| | `WAIT` | 等待操作 | duration |
| | `BACK` | 返回操作 | 无 |
| **元素操作** | `FIND_AND_CLICK` | 查找并点击 | findMethod, targetText |
| | `LONG_CLICK` | 长按操作 | findMethod, targetText |
| | `DOUBLE_CLICK` | 双击操作 | findMethod, targetText, clickInterval |
| | `INPUT_TEXT` | 输入文本 | text |
| | `PASTE_TEXT` | 粘贴文本 | findMethod, targetText, text |
| | `SET_TEXT` | 设置文本 | findMethod, targetText, text |
| | `SELECT_TEXT` | 选择文本 | findMethod, targetText, selectionStart, selectionEnd |
| **手势操作** | `GESTURE_CLICK` | 手势点击 | x, y, duration |
| | `SWIPE` | 滑动手势 | startX, startY, endX, endY, duration |
| | `SCROLL_LIST` | 滚动列表 | direction, distance |
| **查找操作** | `FIND_BY_ID` | 通过ID查找 | viewId, 过滤条件 |
| | `FIND_BY_TAGS` | 多条件查找 | className, viewId, text, des |
| **系统操作** | `SYSTEM_HOME` | 返回主屏幕 | 无 |
| | `SYSTEM_NOTIFICATIONS` | 打开通知栏 | 无 |
| | `SYSTEM_RECENT_APPS` | 显示最近任务 | 无 |
| **截图操作** | `TAKE_SCREENSHOT` | 截图操作 | saveToFile, fileName, format |
| | `TAKE_ELEMENT_SCREENSHOT` | 元素截图 | findMethod, targetText, saveToFile |

### 支持的成功条件（10种）

| 类别 | 条件类型 | 说明 | 主要参数 |
|------|----------|------|----------|
| **元素查找** | `FIND_TEXT` | 查找文本 | targetText, positionCheck |
| | `UI_ELEMENT_EXISTS` | UI元素存在 | text, className, viewId |
| | `FIND_BY_ID` | 通过ID查找 | viewId, 过滤条件 |
| | `FIND_BY_TAGS` | 多条件查找 | className, viewId, text, des |
| **状态检查** | `PACKAGE_RUNNING` | 应用运行状态 | packageName |
| | `ACTIVITY_MATCH` | Activity匹配 | expectedPackage, expectedActivity |
| **内容匹配** | `UI_CONTAINS_ANY` | 包含任意关键词 | keywords, matchMode |
| | `UI_CONTAINS_ALL` | 包含所有关键词 | keywords, matchMode |
| **数量尺寸** | `ELEMENT_COUNT` | 元素数量检查 | findMethod, targetText, expectedCount, operator |
| | `SCREEN_SIZE_MATCH` | 屏幕尺寸匹配 | expectedWidth, expectedHeight, tolerance |

## 完整数据结构

### 根对象结构

```json
{
  "sessionId": "string",           // 必需：会话唯一标识符
  "stepId": "string",              // 必需：步骤唯一标识符
  "stepName": "string",            // 必需：步骤名称
  "stepDescription": "string",     // 可选：步骤描述，用于调试和日志
  "action": {                      // 必需：动作定义
    "type": "string",              // 必需：动作类型
    "params": {},                  // 必需：动作参数（根据type变化）
    "timeout": 10000               // 必需：超时时间（毫秒）
  },
  "successConditions": [],         // 可选：成功条件数组
  "retryConfig": {                 // 可选：重试配置
    "maxRetries": 3,               // 最大重试次数
    "retryDelay": 1000             // 重试间隔（毫秒）
  },
  "needsFeedback": true            // 必需：是否需要反馈
}
```

## 字段详细说明

### 1. 基础字段

| 字段名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| `sessionId` | String | ✅ | 会话唯一标识符，用于关联多个步骤 | "session_001" |
| `stepId` | String | ✅ | 步骤唯一标识符，在会话内唯一 | "step_001" |
| `stepName` | String | ✅ | 步骤名称，简短描述 | "启动微信" |
| `stepDescription` | String | ❌ | 步骤详细描述，用于调试和日志 | "启动微信应用" |
| `needsFeedback` | Boolean | ✅ | 是否需要执行反馈 | true |

### 2. Action 动作对象

#### 基础结构
```json
{
  "type": "ACTION_TYPE",
  "params": {
    // 根据type不同而变化的参数
  },
  "timeout": 10000
}
```

#### 支持的动作类型

##### 2.1 LAUNCH_APP - 启动应用
```json
{
  "type": "LAUNCH_APP",
  "params": {
    "packageName": "com.tencent.mm",           // 必需：应用包名
    "activityName": "com.tencent.mm.ui.LauncherUI"  // 可选：Activity名称
  },
  "timeout": 10000
}
```

**参数说明：**
- `packageName`: 目标应用的包名
- `activityName`: 启动的Activity名称（可选，系统会自动选择默认Activity）

##### 2.2 FIND_AND_CLICK - 查找并点击
```json
{
  "type": "FIND_AND_CLICK",
  "params": {
    "findMethod": "findByText",                // 必需：查找方法
    "targetText": "发现",                      // 必需：目标文本
    "positionCheck": {                         // 可选：位置检查
      "minX": 100,
      "minY": 200,
      "maxX": 500,
      "maxY": 600,
      "baseWidth": 1080,
      "baseHeight": 1920
    }
  },
  "timeout": 5000
}
```

**参数说明：**
- `findMethod`: 查找方法，支持：
  - `"findByText"`: 通过文本查找
  - `"findByTextAllMatch"`: 通过文本完全匹配查找
  - `"findById"`: 通过资源ID查找
  - `"findByTags"`: 通过多条件查找
- `targetText`: 要查找的目标文本
- `positionCheck`: 位置验证（可选）
  - `minX/minY`: 最小坐标
  - `maxX/maxY`: 最大坐标
  - `baseWidth/baseHeight`: 基准屏幕尺寸

##### 2.3 INPUT_TEXT - 输入文本
```json
{
  "type": "INPUT_TEXT",
  "params": {
    "text": "Hello World"                        // 必需：要输入的文本
  },
  "timeout": 3000
}
```

**参数说明：**
- `text`: 要输入的文本内容

##### 2.4 SCROLL_LIST - 滚动列表
```json
{
  "type": "SCROLL_LIST",
  "params": {
    "direction": "down",                      // 必需：滚动方向
    "distance": 500                          // 可选：滚动距离（像素）
  },
  "timeout": 2000
}
```

**参数说明：**
- `direction`: 滚动方向
  - `"up"`: 向上滚动
  - `"down"`: 向下滚动
  - `"left"`: 向左滚动
  - `"right"`: 向右滚动
- `distance`: 滚动距离（像素），默认500

##### 2.5 WAIT - 等待
```json
{
  "type": "WAIT",
  "params": {
    "duration": 2000                         // 必需：等待时长（毫秒）
  },
  "timeout": 5000
}
```

**参数说明：**
- `duration`: 等待时长（毫秒）

##### 2.6 BACK - 返回操作
```json
{
  "type": "BACK",
  "params": {},                              // 无参数
  "timeout": 1000
}
```

**参数说明：**
- 无需参数，执行系统返回操作

##### 2.7 LONG_CLICK - 长按操作
```json
{
  "type": "LONG_CLICK",
  "params": {
    "findMethod": "findByText",                // 必需：查找方法
    "targetText": "长按目标",                  // 必需：目标文本
    "positionCheck": null                      // 可选：位置检查
  },
  "timeout": 3000
}
```

**参数说明：**
- `findMethod`: 查找方法，支持 "findByText", "findById", "findByTags"
- `targetText`: 要查找的目标文本
- `positionCheck`: 位置验证（可选）

##### 2.8 PASTE_TEXT - 粘贴文本
```json
{
  "type": "PASTE_TEXT",
  "params": {
    "findMethod": "findByText",                // 必需：查找方法
    "targetText": "输入框",                    // 必需：目标元素文本
    "text": "要粘贴的内容"                     // 必需：粘贴的文本内容
  },
  "timeout": 3000
}
```

**参数说明：**
- `findMethod`: 查找输入框的方法
- `targetText`: 目标输入框的文本或标识
- `text`: 要粘贴的文本内容

##### 2.9 SET_TEXT - 设置文本
```json
{
  "type": "SET_TEXT",
  "params": {
    "findMethod": "findByText",                // 必需：查找方法
    "targetText": "输入框",                    // 必需：目标元素文本
    "text": "新的文本内容"                     // 必需：要设置的文本
  },
  "timeout": 3000
}
```

**参数说明：**
- `findMethod`: 查找输入框的方法
- `targetText`: 目标输入框的文本或标识
- `text`: 要设置的新文本内容

##### 2.10 SELECT_TEXT - 选择文本
```json
{
  "type": "SELECT_TEXT",
  "params": {
    "findMethod": "findByText",                // 必需：查找方法
    "targetText": "文本框",                    // 必需：目标元素文本
    "selectionStart": 0,                       // 必需：选择开始位置
    "selectionEnd": 5                          // 必需：选择结束位置
  },
  "timeout": 3000
}
```

**参数说明：**
- `findMethod`: 查找文本框的方法
- `targetText`: 目标文本框的文本或标识
- `selectionStart`: 选择文本的开始位置
- `selectionEnd`: 选择文本的结束位置

##### 2.11 GESTURE_CLICK - 手势点击
```json
{
  "type": "GESTURE_CLICK",
  "params": {
    "x": 540,                                  // 必需：X坐标
    "y": 960,                                  // 必需：Y坐标
    "duration": 25,                            // 可选：点击持续时间（毫秒）
    "baseWidth": 1080,                         // 可选：基准屏幕宽度
    "baseHeight": 1920                         // 可选：基准屏幕高度
  },
  "timeout": 2000
}
```

**参数说明：**
- `x`: 点击的X坐标
- `y`: 点击的Y坐标
- `duration`: 点击持续时间（毫秒），默认25ms
- `baseWidth/baseHeight`: 基准屏幕尺寸，用于坐标适配

##### 2.12 DOUBLE_CLICK - 双击操作
```json
{
  "type": "DOUBLE_CLICK",
  "params": {
    "findMethod": "findByText",                // 必需：查找方法
    "targetText": "双击目标",                  // 必需：目标文本
    "clickInterval": 100,                      // 可选：两次点击间隔（毫秒）
    "offsetX": 0,                              // 可选：X轴偏移
    "offsetY": 0                               // 可选：Y轴偏移
  },
  "timeout": 3000
}
```

**参数说明：**
- `findMethod`: 查找方法
- `targetText`: 目标元素文本
- `clickInterval`: 两次点击的间隔时间（毫秒）
- `offsetX/offsetY`: 相对于元素中心的偏移量

##### 2.13 SWIPE - 滑动手势
```json
{
  "type": "SWIPE",
  "params": {
    "startX": 540,                             // 必需：起始X坐标
    "startY": 1000,                            // 必需：起始Y坐标
    "endX": 540,                               // 必需：结束X坐标
    "endY": 500,                               // 必需：结束Y坐标
    "duration": 300,                           // 可选：滑动持续时间（毫秒）
    "baseWidth": 1080,                         // 可选：基准屏幕宽度
    "baseHeight": 1920                         // 可选：基准屏幕高度
  },
  "timeout": 2000
}
```

**参数说明：**
- `startX/startY`: 滑动起始坐标
- `endX/endY`: 滑动结束坐标
- `duration`: 滑动持续时间（毫秒），默认300ms
- `baseWidth/baseHeight`: 基准屏幕尺寸，用于坐标适配

##### 2.14 LAUNCH_APP_BY_INTENT - 通过Intent启动应用
```json
{
  "type": "LAUNCH_APP_BY_INTENT",
  "params": {
    "action": "android.intent.action.VIEW",   // 必需：Intent动作
    "data": "https://www.example.com",        // 可选：Intent数据
    "category": "android.intent.category.DEFAULT", // 可选：Intent类别
    "extras": {                               // 可选：额外参数
      "key1": "value1",
      "key2": "value2"
    }
  },
  "timeout": 10000
}
```

**参数说明：**
- `action`: Intent的动作类型
- `data`: Intent携带的数据（如URL、文件路径等）
- `category`: Intent的类别
- `extras`: 额外的键值对参数

##### 2.15 TAKE_SCREENSHOT - 截图操作 (Android R+)
```json
{
  "type": "TAKE_SCREENSHOT",
  "params": {
    "saveToFile": true,                       // 可选：是否保存到文件
    "fileName": "screenshot.png",             // 可选：文件名
    "format": "PNG",                          // 可选：图片格式 PNG/JPEG
    "quality": 100                            // 可选：图片质量 (0-100)
  },
  "timeout": 5000
}
```

**参数说明：**
- `saveToFile`: 是否保存截图到文件
- `fileName`: 保存的文件名
- `format`: 图片格式，支持PNG、JPEG
- `quality`: 图片质量（0-100），仅对JPEG有效

##### 2.16 TAKE_ELEMENT_SCREENSHOT - 元素截图 (Android R+)
```json
{
  "type": "TAKE_ELEMENT_SCREENSHOT",
  "params": {
    "findMethod": "findByText",               // 必需：查找方法
    "targetText": "截图目标",                 // 必需：目标元素文本
    "saveToFile": true,                       // 可选：是否保存到文件
    "fileName": "element_screenshot.png",     // 可选：文件名
    "format": "PNG"                           // 可选：图片格式
  },
  "timeout": 5000
}
```

**参数说明：**
- `findMethod`: 查找目标元素的方法
- `targetText`: 目标元素的文本或标识
- `saveToFile`: 是否保存截图到文件
- `fileName`: 保存的文件名
- `format`: 图片格式

##### 2.17 SYSTEM_HOME - 返回主屏幕
```json
{
  "type": "SYSTEM_HOME",
  "params": {},                              // 无参数
  "timeout": 2000
}
```

##### 2.18 SYSTEM_NOTIFICATIONS - 打开通知栏
```json
{
  "type": "SYSTEM_NOTIFICATIONS",
  "params": {},                              // 无参数
  "timeout": 2000
}
```

##### 2.19 SYSTEM_RECENT_APPS - 显示最近任务
```json
{
  "type": "SYSTEM_RECENT_APPS",
  "params": {},                              // 无参数
  "timeout": 2000
}
```

##### 2.20 FIND_BY_ID - 通过ID查找
```json
{
  "type": "FIND_BY_ID",
  "params": {
    "viewId": "com.example.app:id/button",   // 必需：元素ID
    "filterText": "按钮",                     // 可选：文本过滤
    "filterDes": "描述",                      // 可选：描述过滤
    "filterClass": "android.widget.Button"   // 可选：类名过滤
  },
  "timeout": 3000
}
```

**参数说明：**
- `viewId`: 目标元素的资源ID
- `filterText`: 文本内容过滤条件
- `filterDes`: 描述内容过滤条件
- `filterClass`: 元素类名过滤条件

##### 2.21 FIND_BY_TAGS - 通过多条件查找
```json
{
  "type": "FIND_BY_TAGS",
  "params": {
    "className": "android.widget.TextView",  // 必需：元素类名
    "viewId": "com.example.app:id/text",     // 可选：元素ID
    "text": "文本内容",                       // 可选：文本内容
    "des": "描述内容"                        // 可选：描述内容
  },
  "timeout": 3000
}
```

**参数说明：**
- `className`: 目标元素的类名
- `viewId`: 元素的资源ID（可选）
- `text`: 元素的文本内容（可选）
- `des`: 元素的描述内容（可选）

### 3. SuccessConditions 成功条件

成功条件用于验证步骤是否执行成功。支持多个条件，全部满足才算成功。

#### 基础结构
```json
{
  "type": "CONDITION_TYPE",
  "params": {
    // 根据type不同而变化的参数
  }
}
```

#### 支持的条件类型

##### 3.1 FIND_TEXT - 查找文本
```json
{
  "type": "FIND_TEXT",
  "params": {
    "targetText": "通讯录",                    // 必需：目标文本
    "positionCheck": {                        // 可选：位置检查
      "minX": 340,
      "minY": 1850,
      "baseWidth": 1080,
      "baseHeight": 1920
    }
  }
}
```

**参数说明：**
- `targetText`: 要查找的文本
- `positionCheck`: 位置验证（可选）

##### 3.2 UI_ELEMENT_EXISTS - UI元素存在
```json
{
  "type": "UI_ELEMENT_EXISTS",
  "params": {
    "text": "微信",                           // 可选：元素文本
    "className": "android.widget.TextView", // 可选：元素类名
    "viewId": "com.tencent.mm:id/title",   // 可选：元素ID
    "timeout": 5000                        // 可选：查找超时
  }
}
```

**参数说明：**
- `text`: 元素包含的文本
- `className`: 元素的类名
- `viewId`: 元素的资源ID
- `timeout`: 查找超时时间

##### 3.3 FIND_BY_ID - 通过ID查找元素
```json
{
  "type": "FIND_BY_ID",
  "params": {
    "viewId": "com.tencent.mm:id/title",     // 必需：元素ID
    "filterText": "微信",                     // 可选：文本过滤
    "filterDes": "应用标题",                  // 可选：描述过滤
    "filterClass": "android.widget.TextView" // 可选：类名过滤
  }
}
```

**参数说明：**
- `viewId`: 目标元素的资源ID
- `filterText`: 文本内容过滤条件（可选）
- `filterDes`: 描述内容过滤条件（可选）
- `filterClass`: 元素类名过滤条件（可选）

##### 3.4 FIND_BY_TAGS - 通过多条件查找
```json
{
  "type": "FIND_BY_TAGS",
  "params": {
    "className": "android.widget.Button",    // 必需：元素类名
    "viewId": "com.example:id/btn",          // 可选：元素ID
    "text": "确定",                          // 可选：文本内容
    "des": "确认按钮"                        // 可选：描述内容
  }
}
```

**参数说明：**
- `className`: 目标元素的类名
- `viewId`: 元素的资源ID（可选）
- `text`: 元素的文本内容（可选）
- `des`: 元素的描述内容（可选）

##### 3.5 PACKAGE_RUNNING - 检查应用运行状态
```json
{
  "type": "PACKAGE_RUNNING",
  "params": {
    "packageName": "com.tencent.mm"          // 必需：应用包名
  }
}
```

**参数说明：**
- `packageName`: 要检查的应用包名

##### 3.6 ACTIVITY_MATCH - 检查Activity匹配
```json
{
  "type": "ACTIVITY_MATCH",
  "params": {
    "expectedPackage": "com.tencent.mm",     // 必需：期望的包名
    "expectedActivity": "MainActivity"        // 可选：期望的Activity名称
  }
}
```

**参数说明：**
- `expectedPackage`: 期望的应用包名
- `expectedActivity`: 期望的Activity名称（可选）

##### 3.7 UI_CONTAINS_ANY - 包含任意关键词
```json
{
  "type": "UI_CONTAINS_ANY",
  "params": {
    "keywords": ["微信", "通讯录", "发现", "我"], // 必需：关键词列表
    "matchMode": "partial"                    // 可选：匹配模式 partial/exact
  }
}
```

**参数说明：**
- `keywords`: 关键词列表，满足任意一个即可
- `matchMode`: 匹配模式
  - `"partial"`: 部分匹配（默认）
  - `"exact"`: 精确匹配

##### 3.8 UI_CONTAINS_ALL - 包含所有关键词
```json
{
  "type": "UI_CONTAINS_ALL",
  "params": {
    "keywords": ["微信", "通讯录"],           // 必需：关键词列表
    "matchMode": "partial"                    // 可选：匹配模式
  }
}
```

**参数说明：**
- `keywords`: 关键词列表，必须全部满足
- `matchMode`: 匹配模式（同上）

##### 3.9 ELEMENT_COUNT - 元素数量检查
```json
{
  "type": "ELEMENT_COUNT",
  "params": {
    "findMethod": "findByText",               // 必需：查找方法
    "targetText": "按钮",                     // 必需：目标文本
    "expectedCount": 3,                       // 必需：期望数量
    "operator": "eq"                          // 可选：比较操作符
  }
}
```

**参数说明：**
- `findMethod`: 查找元素的方法
- `targetText`: 目标元素的文本
- `expectedCount`: 期望的元素数量
- `operator`: 比较操作符
  - `"eq"`: 等于（默认）
  - `"gt"`: 大于
  - `"lt"`: 小于
  - `"gte"`: 大于等于
  - `"lte"`: 小于等于

##### 3.10 SCREEN_SIZE_MATCH - 屏幕尺寸匹配
```json
{
  "type": "SCREEN_SIZE_MATCH",
  "params": {
    "expectedWidth": 1080,                    // 可选：期望宽度
    "expectedHeight": 1920,                   // 可选：期望高度
    "tolerance": 50                           // 可选：容差范围
  }
}
```

**参数说明：**
- `expectedWidth`: 期望的屏幕宽度（可选）
- `expectedHeight`: 期望的屏幕高度（可选）
- `tolerance`: 容差范围（像素）

### 4. RetryConfig 重试配置

```json
{
  "maxRetries": 3,                          // 最大重试次数
  "retryDelay": 1000                        // 重试间隔（毫秒）
}
```

**参数说明：**
- `maxRetries`: 最大重试次数，建议1-5次
- `retryDelay`: 重试间隔时间（毫秒），建议500-3000ms

## 完整示例

### 示例1：启动微信应用
```json
{
  "sessionId": "session_001",
  "stepId": "step_001",
  "stepName": "启动微信",
  "stepDescription": "启动微信应用",
  "action": {
    "type": "LAUNCH_APP",
    "params": {
      "packageName": "com.tencent.mm",
      "activityName": "com.tencent.mm.ui.LauncherUI"
    },
    "timeout": 10000
  },
  "successConditions": [
    {
      "type": "FIND_TEXT",
      "params": {
        "targetText": "通讯录",
        "positionCheck": {
          "minX": 340,
          "minY": 1850,
          "baseWidth": 1080,
          "baseHeight": 1920
        }
      }
    }
  ],
  "retryConfig": {
    "maxRetries": 3,
    "retryDelay": 1000
  },
  "needsFeedback": true
}
```

### 示例2：查找并点击元素
```json
{
  "sessionId": "session_001",
  "stepId": "step_002",
  "stepName": "点击发现",
  "stepDescription": "在微信中点击发现按钮",
  "action": {
    "type": "FIND_AND_CLICK",
    "params": {
      "findMethod": "findByText",
      "targetText": "发现",
      "positionCheck": null
    },
    "timeout": 5000
  },
  "successConditions": [
    {
      "type": "FIND_TEXT",
      "params": {
        "targetText": "朋友圈",
        "positionCheck": null
      }
    }
  ],
  "retryConfig": {
    "maxRetries": 3,
    "retryDelay": 1000
  },
  "needsFeedback": true
}
```

### 示例3：输入文本
```json
{
  "sessionId": "session_001",
  "stepId": "step_003",
  "stepName": "输入搜索内容",
  "stepDescription": "在搜索框中输入文本",
  "action": {
    "type": "INPUT_TEXT",
    "params": {
      "text": "张三"
    },
    "timeout": 3000
  },
  "successConditions": [],
  "retryConfig": {
    "maxRetries": 2,
    "retryDelay": 500
  },
  "needsFeedback": true
}
```

### 示例4：等待操作
```json
{
  "sessionId": "session_001",
  "stepId": "step_004",
  "stepName": "等待加载",
  "stepDescription": "等待页面加载完成",
  "action": {
    "type": "WAIT",
    "params": {
      "duration": 2000
    },
    "timeout": 5000
  },
  "successConditions": [],
  "retryConfig": {
    "maxRetries": 1,
    "retryDelay": 1000
  },
  "needsFeedback": true
}
```

## 数据类型约束

### 字符串类型
- `sessionId`: 建议格式 "session_" + 时间戳或UUID
- `stepId`: 建议格式 "step_" + 序号，如 "step_001"
- `stepName`: 长度建议不超过50字符
- `stepDescription`: 长度建议不超过200字符

### 数值类型
- `timeout`: 范围 1000-30000 毫秒
- `maxRetries`: 范围 0-10 次
- `retryDelay`: 范围 100-10000 毫秒
- 坐标值: 基于1080x1920基准分辨率

### 布尔类型
- `needsFeedback`: 通常设为 true

## 基于AssistsCore的扩展能力

### AssistsCore支持的查找方法
基于 `assists/src/main/java/com/ven/assists/AssistsCore.kt` 的能力：

| 方法名 | 说明 | 参数 | 返回值 |
|--------|------|------|--------|
| `findById` | 通过资源ID查找所有符合条件的元素 | id, filterText?, filterDes?, filterClass? | List<AccessibilityNodeInfo> |
| `findByText` | 通过文本内容查找所有符合条件的元素 | text, filterViewId?, filterDes?, filterClass? | List<AccessibilityNodeInfo> |
| `findByTextAllMatch` | 查找所有文本完全匹配的元素 | text, filterViewId?, filterDes?, filterClass? | List<AccessibilityNodeInfo> |
| `findByTags` | 根据多个条件查找元素 | className, viewId?, text?, des? | List<AccessibilityNodeInfo> |
| `getAllNodes` | 获取当前窗口中的所有元素 | filterViewId?, filterDes?, filterClass?, filterText? | List<AccessibilityNodeInfo> |

### AssistsCore支持的操作方法

| 操作类型 | 方法名 | 说明 | 返回值 |
|----------|--------|------|--------|
| **元素操作** | `click()` | 点击元素 | Boolean |
| **元素操作** | `longClick()` | 长按元素 | Boolean |
| **元素操作** | `paste(text)` | 向元素粘贴文本 | Boolean |
| **元素操作** | `setNodeText(text)` | 设置元素的文本内容 | Boolean |
| **元素操作** | `selectionText(start, end)` | 选择元素中的文本 | Boolean |
| **元素操作** | `scrollForward()` | 向前滚动可滚动元素 | Boolean |
| **元素操作** | `scrollBackward()` | 向后滚动可滚动元素 | Boolean |
| **手势操作** | `gestureClick(x, y, duration?)` | 在指定坐标位置执行点击手势 | Boolean (suspend) |
| **手势操作** | `nodeGestureClick(offsetX?, offsetY?, duration?)` | 在元素位置执行点击手势 | Boolean (suspend) |
| **手势操作** | `nodeGestureClickByDouble(offsetX?, offsetY?, clickInterval?)` | 在元素位置执行双击手势 | Boolean (suspend) |
| **手势操作** | `gesture(startLocation, endLocation, startTime, duration)` | 执行点击或滑动手势 | Boolean (suspend) |
| **手势操作** | `gesture(path, startTime, duration)` | 执行自定义路径的手势 | Boolean (suspend) |
| **手势操作** | `dispatchGesture(gesture, delay?)` | 执行手势操作 | Boolean (suspend) |
| **系统操作** | `back()` | 执行返回操作 | Boolean |
| **系统操作** | `home()` | 返回主屏幕 | Boolean |
| **系统操作** | `notifications()` | 打开通知栏 | Boolean |
| **系统操作** | `recentApps()` | 显示最近任务 | Boolean |
| **应用启动** | `launchApp(intent)` | 通过Intent启动应用 | Boolean (suspend) |
| **应用启动** | `launchApp(packageName)` | 通过包名启动应用 | Boolean (suspend) |
| **屏幕截图** | `takeScreenshot()` | 截取整个屏幕 | Bitmap? (suspend) |
| **屏幕截图** | `takeScreenshotSave(file?, format?)` | 截取整个屏幕并保存到文件 | File? (suspend) |
| **元素截图** | `takeScreenshot()` | 截取指定元素的屏幕截图 | Bitmap? (suspend) |
| **元素截图** | `takeScreenshotSave(file?, format?)` | 截取指定元素的屏幕截图并保存到文件 | File? (suspend) |

## 错误处理

### 常见错误类型
1. **JSON格式错误**: 确保JSON语法正确
2. **必需字段缺失**: 检查所有必需字段是否存在
3. **参数类型错误**: 确保参数类型与规范一致
4. **动作类型不支持**: 使用支持的动作类型
5. **超时值过小**: 确保超时值合理

### 调试建议
1. 使用JSON验证工具检查格式
2. 检查日志中的解析错误信息
3. 验证参数值的合理性
4. 测试时使用较大的超时值

## 扩展性

### 当前已支持的动作类型（v1.0）
基于AssistsCore的完整能力，当前版本支持21种动作类型：

**基础操作：**
- ✅ `LAUNCH_APP`: 启动应用
- ✅ `LAUNCH_APP_BY_INTENT`: 通过Intent启动应用
- ✅ `WAIT`: 等待操作
- ✅ `BACK`: 返回操作

**元素操作：**
- ✅ `FIND_AND_CLICK`: 查找并点击
- ✅ `LONG_CLICK`: 长按操作
- ✅ `DOUBLE_CLICK`: 双击操作
- ✅ `INPUT_TEXT`: 输入文本
- ✅ `PASTE_TEXT`: 粘贴文本
- ✅ `SET_TEXT`: 设置文本
- ✅ `SELECT_TEXT`: 选择文本

**手势操作：**
- ✅ `GESTURE_CLICK`: 手势点击
- ✅ `SWIPE`: 滑动手势
- ✅ `SCROLL_LIST`: 滚动列表

**查找操作：**
- ✅ `FIND_BY_ID`: 通过ID查找
- ✅ `FIND_BY_TAGS`: 通过多条件查找

**系统操作：**
- ✅ `SYSTEM_HOME`: 返回主屏幕
- ✅ `SYSTEM_NOTIFICATIONS`: 打开通知栏
- ✅ `SYSTEM_RECENT_APPS`: 显示最近任务

**截图操作（Android R+）：**
- ✅ `TAKE_SCREENSHOT`: 截图操作
- ✅ `TAKE_ELEMENT_SCREENSHOT`: 元素截图

### 未来可能扩展的动作类型
- `CUSTOM_GESTURE`: 自定义复杂手势路径
- `MULTI_TOUCH`: 多点触控操作
- `DRAG_AND_DROP`: 拖拽操作
- `PINCH_ZOOM`: 缩放手势
- `ROTATE_GESTURE`: 旋转手势

### 当前已支持的成功条件（v1.0）
基于AssistsCore的完整能力，当前版本支持10种成功条件：

**元素查找条件：**
- ✅ `FIND_TEXT`: 查找文本
- ✅ `UI_ELEMENT_EXISTS`: UI元素存在
- ✅ `FIND_BY_ID`: 通过ID查找元素
- ✅ `FIND_BY_TAGS`: 通过多条件查找

**状态检查条件：**
- ✅ `PACKAGE_RUNNING`: 检查应用运行状态
- ✅ `ACTIVITY_MATCH`: 检查Activity匹配

**内容匹配条件：**
- ✅ `UI_CONTAINS_ANY`: 包含任意关键词
- ✅ `UI_CONTAINS_ALL`: 包含所有关键词

**数量和尺寸条件：**
- ✅ `ELEMENT_COUNT`: 元素数量检查
- ✅ `SCREEN_SIZE_MATCH`: 屏幕尺寸匹配

### 未来可能扩展的成功条件
- `DYNAMIC_ANALYSIS`: 动态AI分析
- `ELEMENT_VISIBLE`: 元素可见性检查
- `ELEMENT_CLICKABLE`: 元素可点击性检查
- `TEXT_MATCH_REGEX`: 正则表达式文本匹配
- `IMAGE_SIMILARITY`: 图像相似度匹配
- `PERFORMANCE_METRIC`: 性能指标检查

## 版本兼容性

当前版本：v1.0
- ✅ 支持21种动作类型（基础操作、元素操作、手势操作、系统操作、截图操作）
- ✅ 支持10种成功条件类型（元素查找、状态检查、内容匹配、数量尺寸）
- ✅ 支持完整的重试机制和超时控制
- ✅ 支持位置检查和坐标适配功能
- ✅ 支持4种查找方法（文本、ID、标签、完全匹配）
- ✅ 基于AssistsCore v1.0的完整无障碍服务能力
- ✅ 支持Android R+的截图功能
- ✅ 支持协程异步操作

## 注意事项

1. **坐标适配**: 位置检查基于1080x1920分辨率，系统会自动适配其他分辨率
2. **超时设置**: 不同动作类型建议使用不同的超时时间
   - LAUNCH_APP: 10000ms
   - FIND_AND_CLICK: 5000ms
   - INPUT_TEXT: 3000ms
   - SCROLL_LIST: 2000ms
   - WAIT: duration + 1000ms
   - BACK: 1000ms
3. **重试策略**: 网络相关操作建议设置重试，UI操作可以不设置
4. **反馈机制**: 建议所有步骤都开启反馈以便调试和监控
5. **性能考虑**: 避免过于频繁的操作，适当添加等待时间
6. **无障碍权限**: 确保应用已获得无障碍服务权限
7. **应用兼容性**: 不同应用的UI结构可能不同，需要适配测试

## 反馈数据结构

当 `needsFeedback: true` 时，App端会返回以下结构的反馈数据：

```json
{
  "sessionId": "session_001",
  "stepId": "step_001",
  "executionResult": {
    "status": "SUCCESS",              // SUCCESS, FAILED, RETRY_NEEDED
    "message": "步骤执行成功",
    "executionTime": 1640995200000,
    "retryCount": 0
  },
  "currentUI": {
    "packageName": "com.tencent.mm",
    "activityName": "unknown",
    "uiElements": [
      {
        "text": "微信",
        "className": "android.widget.TextView",
        "bounds": {
          "left": 100,
          "top": 200,
          "right": 300,
          "bottom": 250
        },
        "clickable": true,
        "scrollable": false,
        "viewId": "com.tencent.mm:id/title"
      }
    ],
    "screenSize": {
      "width": 1080,
      "height": 1920
    }
  },
  "logs": [
    "执行动作: LAUNCH_APP",
    "成功启动应用: com.tencent.mm"
  ],
  "timestamp": 1640995200000
}
```

## 使用建议和最佳实践

### 1. 动作类型选择建议

| 场景 | 推荐动作类型 | 理由 |
|------|-------------|------|
| 启动应用 | `LAUNCH_APP` | 简单可靠，自动选择默认Activity |
| 复杂启动 | `LAUNCH_APP_BY_INTENT` | 支持自定义参数和数据传递 |
| 普通点击 | `FIND_AND_CLICK` | 基于文本查找，适应性强 |
| 精确点击 | `GESTURE_CLICK` | 基于坐标，精确但需要适配 |
| 文本输入 | `INPUT_TEXT` | 适用于简单文本输入 |
| 复杂文本 | `SET_TEXT` + `SELECT_TEXT` | 适用于编辑现有文本 |
| 页面滚动 | `SCROLL_LIST` | 自动适配滚动方向 |
| 自定义滑动 | `SWIPE` | 精确控制滑动路径 |

### 2. 超时时间建议

| 动作类型 | 建议超时时间 | 说明 |
|----------|-------------|------|
| `LAUNCH_APP` | 10000-15000ms | 应用启动需要较长时间 |
| `FIND_AND_CLICK` | 3000-5000ms | 查找元素通常较快 |
| `INPUT_TEXT` | 2000-3000ms | 文本输入速度较快 |
| `SWIPE` | 1000-2000ms | 手势操作执行较快 |
| `TAKE_SCREENSHOT` | 3000-5000ms | 截图处理需要时间 |
| `SYSTEM_*` | 1000-2000ms | 系统操作响应较快 |

### 3. 成功条件设计原则

**优先级排序：**
1. **状态检查** > **元素查找** > **内容匹配**
2. **精确匹配** > **模糊匹配** > **数量检查**

**组合使用建议：**
```json
"successConditions": [
  {
    "type": "PACKAGE_RUNNING",
    "params": { "packageName": "com.tencent.mm" }
  },
  {
    "type": "UI_CONTAINS_ANY",
    "params": { "keywords": ["微信", "通讯录", "发现"] }
  }
]
```

### 4. 错误处理策略

**重试配置建议：**
- **网络相关操作**: maxRetries=3, retryDelay=2000ms
- **UI查找操作**: maxRetries=2, retryDelay=1000ms
- **手势操作**: maxRetries=1, retryDelay=500ms
- **系统操作**: maxRetries=0（通常不需要重试）

### 5. 性能优化建议

**减少执行时间：**
- 使用精确的查找条件，避免全局搜索
- 合理设置超时时间，避免过长等待
- 优先使用`findByText`而非`findByTags`
- 避免不必要的截图操作

**提高成功率：**
- 使用多个成功条件进行验证
- 设置合理的位置检查范围
- 考虑不同屏幕尺寸的适配
- 添加适当的等待时间

### 6. 调试技巧

**日志分析：**
- 关注`executionResult.message`中的错误信息
- 检查`currentUI.uiElements`中的元素信息
- 分析`logs`数组中的执行步骤

**常见问题排查：**
- **元素找不到**: 检查文本内容、ID、类名是否正确
- **点击无效**: 确认元素是否可点击，考虑使用父元素
- **超时错误**: 增加超时时间或优化查找条件
- **坐标偏移**: 检查基准分辨率设置和坐标计算

### 7. 安全注意事项

- 避免在敏感操作中使用截图功能
- 谨慎处理包含个人信息的文本输入
- 确保应用具有必要的权限（无障碍服务、截图权限等）
- 在生产环境中限制调试日志的输出
```

### 3. SuccessConditions 成功条件

成功条件用于验证步骤是否执行成功。支持多个条件，全部满足才算成功。

#### 基础结构
```json
{
  "type": "CONDITION_TYPE",
  "params": {
    // 根据type不同而变化的参数
  }
}
```

#### 支持的条件类型

##### 3.1 FIND_TEXT - 查找文本
```json
{
  "type": "FIND_TEXT",
  "params": {
    "targetText": "通讯录",                    // 必需：目标文本
    "positionCheck": {                        // 可选：位置检查
      "minX": 340,
      "minY": 1850,
      "baseWidth": 1080,
      "baseHeight": 1920
    }
  }
}
```

**参数说明：**
- `targetText`: 要查找的文本
- `positionCheck`: 位置验证（可选）

##### 3.2 UI_ELEMENT_EXISTS - UI元素存在
```json
{
  "type": "UI_ELEMENT_EXISTS",
  "params": {
    "text": "微信",                           // 可选：元素文本
    "className": "android.widget.TextView", // 可选：元素类名
    "viewId": "com.tencent.mm:id/title",   // 可选：元素ID
    "timeout": 5000                        // 可选：查找超时
  }
}
```

**参数说明：**
- `text`: 元素包含的文本
- `className`: 元素的类名
- `viewId`: 元素的资源ID
- `timeout`: 查找超时时间

### 4. RetryConfig 重试配置

```json
{
  "maxRetries": 3,                          // 最大重试次数
  "retryDelay": 1000                        // 重试间隔（毫秒）
}
```

**参数说明：**
- `maxRetries`: 最大重试次数，建议1-5次
- `retryDelay`: 重试间隔时间（毫秒），建议500-3000ms

## 完整示例

### 示例1：启动微信应用
```json
{
  "sessionId": "session_001",
  "stepId": "step_001",
  "stepName": "启动微信",
  "stepDescription": "启动微信应用",
  "action": {
    "type": "LAUNCH_APP",
    "params": {
      "packageName": "com.tencent.mm",
      "activityName": "com.tencent.mm.ui.LauncherUI"
    },
    "timeout": 10000
  },
  "successConditions": [
    {
      "type": "FIND_TEXT",
      "params": {
        "targetText": "通讯录",
        "positionCheck": {
          "minX": 340,
          "minY": 1850,
          "baseWidth": 1080,
          "baseHeight": 1920
        }
      }
    }
  ],
  "retryConfig": {
    "maxRetries": 3,
    "retryDelay": 1000
  },
  "needsFeedback": true
}
```

### 示例2：查找并点击元素
```json
{
  "sessionId": "session_001",
  "stepId": "step_002",
  "stepName": "点击发现",
  "stepDescription": "在微信中点击发现按钮",
  "action": {
    "type": "FIND_AND_CLICK",
    "params": {
      "findMethod": "findByText",
      "targetText": "发现",
      "positionCheck": null
    },
    "timeout": 5000
  },
  "successConditions": [
    {
      "type": "FIND_TEXT",
      "params": {
        "targetText": "朋友圈",
        "positionCheck": null
      }
    }
  ],
  "retryConfig": {
    "maxRetries": 3,
    "retryDelay": 1000
  },
  "needsFeedback": true
}
```