# App接收脚本元数据之后的处理方案（分步执行版）

## 📋 数据结构合理性分析

### ✅ 当前服务端JSON格式分析
**服务端当前发送的JSON数据**（来自 `TestController.requestOpenWechat()`）：
```json
{
  "sessionId": "session_001",
  "stepId": "step_001", 
  "stepName": "启动微信",
  "stepDescription": "启动微信应用并验证是否成功打开",
  "targetApp": {
    "packageName": "com.tencent.mm",
    "activityName": "com.tencent.mm.ui.LauncherUI"
  },
  "action": {
    "type": "LAUNCH_APP",
    "params": {
      "packageName": "com.tencent.mm",
      "activityName": "com.tencent.mm.ui.LauncherUI"
    },
    "timeout": 5000
  },
  "successConditions": [
    {
      "type": "FIND_TEXT",
      "params": {
        "targetText": "通讯录",
        "positionCheck": {
          "minX": 340,
          "minY": 1850,
          "baseWidth": 1080,
          "baseHeight": 1920
        }
      }
    }
  ],
  "retryConfig": {
    "maxRetries": 3,
    "retryDelay": 1000
  },
  "needsFeedback": true,
  "expectedNextAction": "点击通讯录"
}
```

### 🎯 结论：数据结构完全合理
**服务端当前发送的JSON格式与文档建议的单步骤元数据格式完全一致！**

✅ **优势**：
- 包含完整的单步骤执行信息
- 支持动作参数和超时配置
- 包含成功条件验证机制
- 支持重试配置
- 包含反馈需求标识
- 结构清晰，易于解析和扩展

✅ **无需修改服务端**：当前JSON格式已经完美符合分步执行的设计要求

## 核心思路变更

从原来的"一次性发送完整脚本"改为"分步执行，逐步反馈"的模式：

### 原方案问题
- 一次性脚本过于复杂，难以调试
- 执行失败时无法定位具体问题
- 无法根据实际执行情况动态调整

### 新方案优势
- **分步执行**：每次只执行一个具体步骤
- **实时反馈**：每步执行后立即反馈结果和UI状态
- **动态调整**：服务端可根据反馈结果决定下一步操作
- **易于调试**：问题定位更精确

## 实现架构

### 1. 客户端模块协作流程图

```mermaid
sequenceDiagram
    participant User as 👤 用户
    participant MA as MainActivity
    participant NM as NetworkManager
    participant SSM as StepSessionManager
    participant SSP as SingleStepParser
    participant SSE as SingleStepExecutor
    participant AE as ActionExecutor
    participant AC as AssistsCore
    participant UC as UICollector
    participant LW as LogWrapper
    participant SM as StepManager

    Note over User, SM: 🚀 动态脚本执行完整流程

    %% 1. 用户触发阶段
    User->>MA: 1. 点击"请求打开微信"按钮
    MA->>NM: 2. 调用requestOpenWechat()
    NM->>NM: 3. 构建设备信息和请求数据
    NM-->>MA: 4. 发送HTTP请求到服务端

    %% 2. 服务端响应阶段
    Note over MA, NM: 📡 网络通信阶段
    NM->>NM: 5. 接收服务端JSON响应
    NM-->>MA: 6. 返回RequestOpenWechatResponseDto
    MA->>LW: 7. 记录"收到服务端脚本数据"
    MA->>LW: 8. 记录JSON内容到日志

    %% 3. 脚本解析阶段
    Note over MA, SSP: 🔍 JSON解析阶段
    MA->>SSM: 9. 调用executeStep(scriptData)
    SSM->>LW: 10. 记录"开始执行新步骤"
    SSM->>SSP: 11. 创建SingleStepParser实例
    SSP->>SSP: 12. parseFromJson(jsonMetadata)
    SSP->>LW: 13. 记录"步骤解析成功"
    SSP-->>SSM: 14. 返回SingleStepMetadata对象

    %% 4. 执行器创建阶段
    Note over SSM, SSE: 🏗️ 执行器初始化阶段
    SSM->>SSE: 15. 创建SingleStepExecutor实例
    Note right of SSE: 传入stepMetadata和feedbackCallback
    SSM->>SM: 16. 设置StepManager.isStop = false
    SSM->>LW: 17. 记录"开始执行步骤"

    %% 5. 步骤执行阶段
    Note over SSM, AE: ⚡ 动作执行阶段
    SSM->>SSE: 18. 调用executeDirectly()
    SSE->>LW: 19. 记录"开始执行单步骤"
    SSE->>SSE: 20. 调用executeStep(Unit)
    SSE->>AE: 21. 调用ActionExecutor.execute(action)

    %% 6. 具体动作执行
    AE->>AE: 22. 根据action.type选择执行方法
    Note right of AE: 支持LAUNCH_APP, FIND_AND_CLICK等
    AE->>AC: 23. 调用AssistsCore相关方法
    Note right of AC: 如startActivity(), findByText()等
    AC-->>AE: 24. 返回执行结果
    AE-->>SSE: 25. 返回ActionResult

    %% 7. 成功条件检查
    Note over SSE, AC: ✅ 成功条件验证阶段
    SSE->>SSE: 26. 调用checkSuccessConditions()
    SSE->>AC: 27. 调用findByText()查找目标元素
    AC-->>SSE: 28. 返回找到的UI元素列表
    SSE->>SSE: 29. 检查位置条件(如果有)
    SSE->>LW: 30. 记录成功条件检查结果

    %% 8. UI状态收集
    Note over SSE, UC: 📊 UI信息收集阶段
    SSE->>UC: 31. 调用UICollector.collectCurrentUI()
    UC->>AC: 32. 调用getAllNodes()获取所有UI节点
    AC-->>UC: 33. 返回AccessibilityNodeInfo列表
    UC->>UC: 34. 遍历节点收集text, bounds等信息
    UC-->>SSE: 35. 返回UIInfo对象

    %% 9. 反馈数据构建
    Note over SSE, LW: 📤 反馈数据准备阶段
    SSE->>SSE: 36. 调用collectFeedbackData()
    SSE->>LW: 37. 调用getRecentLogs(20)获取最近日志
    LW-->>SSE: 38. 返回日志列表
    SSE->>SSE: 39. 构建FeedbackData对象
    SSE->>SSM: 40. 调用feedbackCallback(feedbackData)

    %% 10. 执行完成和停止
    Note over SSE, SM: 🛑 执行完成阶段
    SSE->>LW: 41. 记录"单步骤执行完成"
    SSE->>SM: 42. 设置StepManager.isStop = true
    SSE-->>SSM: 43. executeDirectly()执行完成

    %% 11. 自动日志发送
    Note over SSM, MA: 📨 自动日志发送阶段
    SSM->>SSM: 44. 等待2秒确保执行完成
    SSM->>LW: 45. 记录"准备自动发送日志"
    SSM->>MA: 46. 调用onStepCompleted回调
    MA->>LW: 47. 记录"自动触发发送日志功能"
    MA->>MA: 48. 调用sendCurrentLogs()
    MA->>NM: 49. 调用uploadLogs()发送日志
    NM-->>MA: 50. 返回上传结果
    MA->>LW: 51. 记录日志发送结果

    Note over User, SM: ✅ 完整流程结束
```

### 2. 模块依赖关系图

```mermaid
graph TB
    subgraph "🎯 用户界面层"
        MA[MainActivity<br/>主界面控制器]
        UI[activity_main.xml<br/>界面布局]
    end

    subgraph "🌐 网络通信层"
        NM[NetworkManager<br/>网络请求管理]
        AS[ApiService<br/>API接口定义]
    end

    subgraph "🚀 脚本执行层"
        SSM[StepSessionManager<br/>会话管理器]
        SSE[SingleStepExecutor<br/>单步骤执行器]
        SSP[SingleStepParser<br/>JSON解析器]
    end

    subgraph "⚡ 动作执行层"
        AE[ActionExecutor<br/>动作执行器]
        UC[UICollector<br/>UI信息收集器]
        FD[FeedbackData<br/>反馈数据结构]
    end

    subgraph "🔧 核心框架层"
        AC[AssistsCore<br/>无障碍服务核心]
        SM[StepManager<br/>步骤管理器]
        SI[StepImpl<br/>步骤实现基类]
    end

    subgraph "📝 工具支持层"
        LW[LogWrapper<br/>日志管理器]
        ST[StepTag<br/>步骤标识]
    end

    %% 用户界面层依赖
    MA --> NM
    MA --> SSM
    MA --> LW
    UI --> MA

    %% 网络通信层依赖
    NM --> AS
    NM --> LW

    %% 脚本执行层依赖
    SSM --> SSE
    SSM --> LW
    SSE --> SSP
    SSE --> AE
    SSE --> UC
    SSE --> FD
    SSE --> SI
    SSE --> SM
    SSE --> LW

    %% 动作执行层依赖
    AE --> AC
    AE --> LW
    UC --> AC
    UC --> LW

    %% 核心框架层依赖
    SM --> SI
    SM --> ST
    AC --> LW

    %% 样式定义
    classDef uiLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef networkLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef scriptLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef actionLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef coreLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef utilLayer fill:#f1f8e9,stroke:#33691e,stroke-width:2px

    class MA,UI uiLayer
    class NM,AS networkLayer
    class SSM,SSE,SSP scriptLayer
    class AE,UC,FD actionLayer
    class AC,SM,SI coreLayer
    class LW,ST utilLayer
```

### 3. 数据流转图

```mermaid
flowchart LR
    subgraph "📡 服务端"
        Server[服务端API<br/>TestController]
    end

    subgraph "📱 Android客户端"
        subgraph "输入数据"
            JSON[JSON元数据<br/>SingleStepMetadata]
            DeviceInfo[设备信息<br/>DeviceInfoDto]
        end

        subgraph "处理过程"
            Parse[解析<br/>SingleStepParser]
            Execute[执行<br/>ActionExecutor]
            Collect[收集<br/>UICollector]
        end

        subgraph "输出数据"
            Feedback[反馈数据<br/>FeedbackData]
            Logs[日志数据<br/>LogUploadRequestDto]
            UIInfo[UI状态<br/>UIInfo]
        end
    end

    %% 数据流向
    Server -->|发送JSON元数据| JSON
    DeviceInfo -->|设备信息| Server

    JSON --> Parse
    Parse -->|解析后的元数据| Execute
    Execute -->|执行结果| Collect
    Collect -->|UI状态| Feedback

    Feedback -->|反馈数据| Logs
    Logs -->|上传日志| Server
    UIInfo -->|界面状态| Server

    %% 样式
    classDef serverStyle fill:#ffcdd2,stroke:#d32f2f,stroke-width:2px
    classDef inputStyle fill:#c8e6c9,stroke:#388e3c,stroke-width:2px
    classDef processStyle fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef outputStyle fill:#e1bee7,stroke:#7b1fa2,stroke-width:2px

    class Server serverStyle
    class JSON,DeviceInfo inputStyle
    class Parse,Execute,Collect processStyle
    class Feedback,Logs,UIInfo outputStyle
```

### 4. 执行流程概述
1. **接收步骤元数据** → JSON解析
2. **执行单个步骤** → 动作执行
3. **检查成功条件** → 验证结果
4. **收集UI状态** → 界面信息
5. **发送反馈数据** → 上传日志
6. **等待下一步骤** → 服务端决策

### 5. 关键模块详细职责

#### 🎯 用户界面层
- **MainActivity**:
  - 用户交互入口点
  - 集成StepSessionManager
  - 处理按钮点击事件
  - 自动触发日志发送
  - 显示执行状态和结果

#### 🌐 网络通信层
- **NetworkManager**:
  - 管理所有HTTP请求
  - 处理请求/响应的序列化
  - 错误处理和重试机制
  - 设备信息收集和发送
- **ApiService**:
  - 定义REST API接口
  - 请求/响应数据结构
  - 网络超时和配置

#### 🚀 脚本执行层
- **StepSessionManager**:
  - 会话生命周期管理
  - 协调各个执行组件
  - 自动触发后续操作
  - 异常处理和恢复
- **SingleStepExecutor**:
  - 单步骤执行控制
  - 基于StepImpl框架
  - 重试机制和超时控制
  - 成功条件验证
  - 反馈数据收集
- **SingleStepParser**:
  - JSON元数据解析
  - 数据结构验证
  - 类型转换和映射
  - 异常处理

#### ⚡ 动作执行层
- **ActionExecutor**:
  - 具体动作执行引擎
  - 支持多种动作类型：
    - LAUNCH_APP: 启动应用
    - FIND_AND_CLICK: 查找并点击
    - INPUT_TEXT: 文本输入
    - SCROLL_LIST: 列表滚动
    - WAIT: 等待操作
    - BACK: 返回操作
  - 位置检查和屏幕适配
- **UICollector**:
  - UI状态信息收集
  - AccessibilityNodeInfo遍历
  - 屏幕尺寸和元素边界
  - 当前Activity识别
- **FeedbackData**:
  - 反馈数据结构定义
  - 执行结果封装
  - UI状态快照
  - 日志信息整合

#### 🔧 核心框架层
- **AssistsCore**:
  - 无障碍服务核心功能
  - UI元素查找和操作
  - 屏幕坐标转换
  - 设备信息获取
- **StepManager**:
  - 步骤执行管理器
  - 协程生命周期控制
  - 步骤注册和调度
  - 停止标志管理
- **StepImpl**:
  - 步骤实现基类
  - 提供协程执行环境
  - 标准化步骤接口

#### 📝 工具支持层
- **LogWrapper**:
  - 日志管理和存储
  - 最近日志获取
  - 日志格式化
  - 异步日志写入
- **StepTag**:
  - 步骤标识常量
  - 步骤流程控制
  - 状态标记

### 6. 错误处理和异常流程

```mermaid
flowchart TD
    Start([开始执行步骤]) --> ParseJSON{JSON解析}

    ParseJSON -->|成功| CreateExecutor[创建执行器]
    ParseJSON -->|失败| LogParseError[记录解析错误]
    LogParseError --> EndWithError([结束-解析失败])

    CreateExecutor --> ExecuteAction{执行动作}

    ExecuteAction -->|成功| CheckConditions{检查成功条件}
    ExecuteAction -->|失败| CheckRetry{检查重试次数}
    ExecuteAction -->|异常| LogException[记录异常]

    CheckRetry -->|未达上限| WaitRetry[等待重试延迟]
    CheckRetry -->|达到上限| CollectFailureFeedback[收集失败反馈]
    WaitRetry --> ExecuteAction

    LogException --> CollectFailureFeedback
    CollectFailureFeedback --> SendFailureFeedback[发送失败反馈]
    SendFailureFeedback --> StopExecution[停止执行]
    StopExecution --> AutoSendLogs[自动发送日志]
    AutoSendLogs --> EndWithFailure([结束-执行失败])

    CheckConditions -->|满足| CollectSuccessFeedback[收集成功反馈]
    CheckConditions -->|不满足| CollectPartialFeedback[收集部分成功反馈]

    CollectSuccessFeedback --> SendSuccessFeedback[发送成功反馈]
    CollectPartialFeedback --> SendPartialFeedback[发送部分成功反馈]

    SendSuccessFeedback --> StopSuccess[停止执行]
    SendPartialFeedback --> StopSuccess

    StopSuccess --> AutoSendLogsSuccess[自动发送日志]
    AutoSendLogsSuccess --> EndWithSuccess([结束-执行成功])

    %% 样式定义
    classDef startEnd fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef process fill:#e3f2fd,stroke:#1565c0,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#ef6c00,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#388e3c,stroke-width:2px

    class Start,EndWithError,EndWithFailure,EndWithSuccess startEnd
    class CreateExecutor,WaitRetry,LogException,CollectSuccessFeedback,CollectPartialFeedback,CollectFailureFeedback,SendSuccessFeedback,SendPartialFeedback,SendFailureFeedback,StopExecution,StopSuccess,AutoSendLogs,AutoSendLogsSuccess process
    class ParseJSON,ExecuteAction,CheckConditions,CheckRetry decision
    class LogParseError error
```

## 已实现功能

### ✅ 完成的核心功能
1. **JSON解析** - 完整解析服务端元数据，包含异常处理
2. **动作执行** - 支持多种自动化动作，包含重试机制
3. **成功条件验证** - 支持文本查找和位置检查
4. **UI状态收集** - 完整的界面信息收集
5. **自动日志发送** - 步骤完成后自动上传日志
6. **单次执行控制** - 避免重复执行问题
7. **错误处理机制** - 完善的异常捕获和处理
8. **重试机制** - 支持配置化的重试策略

### 🎯 执行特点
- **执行一次即停止** - 不会重复执行
- **自动发送日志** - 步骤完成后自动触发
- **完整反馈循环** - 执行 → 停止 → 发送日志 → 服务端接收

## 测试验证

### 当前测试结果
- ✅ 能正确接收服务端JSON数据
- ✅ 能正确解析步骤元数据
- ✅ 能正确执行启动微信动作
- ✅ 执行完成后立即停止
- ✅ 自动触发发送日志功能

### 下一步扩展
1. **更多动作类型** - 根据需要添加新的自动化动作
2. **网络反馈API** - 实现反馈数据发送到服务端
3. **错误处理优化** - 完善异常情况处理
4. **多步骤协调** - 支持复杂的多步骤流程

## 总结

当前实现已经完全满足分步执行的设计要求，服务端JSON格式合理，客户端架构清晰，执行流程稳定。这个文档记录了整个动态脚本执行框架的设计思路和实现细节，对后续的功能扩展和维护具有重要参考价值。
