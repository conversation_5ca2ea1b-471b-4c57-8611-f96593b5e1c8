package com.ven.assists.simple.network

import com.ven.assists.simple.script.FeedbackData
import com.ven.assists.simple.script.StepFeedbackResponseDto
import retrofit2.Response
import retrofit2.http.*

/**
 * REST API接口定义
 */
interface ApiService {

    /**
     * 发送指令信息到服务端
     */
    @POST("api/v1/voice/command")
    suspend fun sendCommand(@Body command: CommandRequestDto): Response<CommandResponseDto>

    /**
     * 获取服务端状态
     */
    @GET("api/v1/health")
    suspend fun getServerStatus(): Response<HealthCheckResponseDto>

    /**
     * 心跳检测
     */
    @POST("api/v1/heartbeat")
    suspend fun heartbeat(@Body heartbeat: HeartbeatRequest): Response<HeartbeatResponse>

    /**
     * 发送日志到服务端
     */
    @POST("api/v1/logs/upload")
    suspend fun uploadLogs(@Body logData: LogUploadRequestDto): Response<LogUploadResponseDto>

    /**
     * 请求打开微信
     */
    @POST("api/v1/script/request-open-wechat")
    suspend fun requestOpenWechat(@Body request: RequestOpenWechatDto): Response<RequestOpenWechatResponseDto>

    /**
     * 发送步骤执行反馈
     */
    @POST("api/v1/step/feedback")
    suspend fun sendStepFeedback(@Body feedback: FeedbackData): Response<StepFeedbackResponseDto>
}

/**
 * 指令请求数据类 - 匹配服务端CommandRequestDto
 */
data class CommandRequestDto(
    val textCommand: TextCommandDto,
    val deviceInfo: DeviceInfoDto,
)

/**
 * 文本指令数据类
 */
data class TextCommandDto(
    val text: String,
    val confidence: Double = 1.0,
    val timestamp: Long = System.currentTimeMillis(),
)

/**
 * 设备信息数据类
 */
data class DeviceInfoDto(
    val model: String,
    val androidVersion: String,
    val screenResolution: String,
    val installedApps: List<String> = emptyList(),
)

/**
 * 指令响应数据类 - 匹配服务端CommandResponseDto
 */
data class CommandResponseDto(
    val sessionId: String,
    val state: String,
    val message: String,
    val estimatedDuration: Long = 0,
)

/**
 * 健康检查响应数据类
 */
data class HealthCheckResponseDto(
    val status: String,
    val timestamp: String,
    val server: ServerInfoDto,
    val config: ConfigInfoDto,
)

/**
 * 服务器信息数据类
 */
data class ServerInfoDto(
    val name: String,
    val version: String,
    val description: String,
)

/**
 * 配置信息数据类
 */
data class ConfigInfoDto(
    val sessionTimeout: Long,
    val maxConcurrentSessions: Int,
    val maxScriptActions: Int,
    val defaultActionTimeout: Long,
)

/**
 * 心跳请求数据类
 */
data class HeartbeatRequest(
    val deviceId: String,
    val timestamp: Long,
)

/**
 * 心跳响应数据类
 */
data class HeartbeatResponse(
    val success: Boolean,
    val serverTime: Long,
)

/**
 * 日志上传请求数据类
 */
data class LogUploadRequestDto(
    val deviceInfo: DeviceInfoDto,
    val logContent: String,
    val logLevel: String = "INFO",
    val timestamp: Long = System.currentTimeMillis(),
    val sessionId: String? = null
)

/**
 * 日志上传响应数据类
 */
data class LogUploadResponseDto(
    val success: Boolean,
    val message: String,
    val logId: String? = null,
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 请求打开微信数据类
 */
data class RequestOpenWechatDto(
    val deviceInfo: DeviceInfoDto,
    val requestId: String = "req_${System.currentTimeMillis()}",
    val timestamp: Long = System.currentTimeMillis()
)

/**
 * 请求打开微信响应数据类
 */
data class RequestOpenWechatResponseDto(
    val success: Boolean,
    val message: String,
    val scriptData: String? = null,
    val timestamp: Long = System.currentTimeMillis()
)
